/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { z } from 'zod';
import { defineTabTool } from './tool.js';
import * as fs from 'fs/promises';
import * as javascript from '../javascript.js';
const htmlSaveSchema = z.object({
    filename: z.string().optional().describe('File name to save the HTML to. Defaults to `page-{timestamp}.html` if not specified.'),
    includeResources: z.boolean().optional().describe('Whether to save linked resources (CSS, JS, images) inline. Default is false.'),
    preserveLinks: z.boolean().optional().describe('Whether to preserve external links. Default is true.'),
});
const htmlSave = defineTabTool({
    capability: 'core',
    schema: {
        name: 'browser_save_html',
        title: 'Save as HTML',
        description: 'Save current page as HTML file with optional resource inlining',
        inputSchema: htmlSaveSchema,
        type: 'readOnly',
    },
    handle: async (tab, params, response) => {
        try {
            const fileName = await tab.context.outputFile(params.filename ?? `page-${new Date().toISOString().replace(/[:.]/g, '-')}.html`);
            let htmlContent;
            if (params.includeResources) {
                // Get HTML with inlined resources
                htmlContent = await tab.page.evaluate(() => {
                    // Clone the document to avoid modifying the original
                    const clonedDoc = document.cloneNode(true);
                    // Inline CSS
                    const styleSheets = Array.from(document.styleSheets);
                    const linkElements = clonedDoc.querySelectorAll('link[rel="stylesheet"]');
                    styleSheets.forEach((sheet, index) => {
                        try {
                            if (sheet.href && linkElements[index]) {
                                const rules = Array.from(sheet.cssRules || []);
                                const cssText = rules.map(rule => rule.cssText).join('\n');
                                const styleElement = clonedDoc.createElement('style');
                                styleElement.textContent = cssText;
                                linkElements[index].replaceWith(styleElement);
                            }
                        }
                        catch (e) {
                            // Skip cross-origin stylesheets
                            console.warn('Could not inline stylesheet:', e);
                        }
                    });
                    // Convert images to base64 (for small images)
                    const images = clonedDoc.querySelectorAll('img');
                    images.forEach(img => {
                        if (img.src && img.src.startsWith('data:')) {
                            // Already base64, keep as is
                            return;
                        }
                        // For now, keep original src - base64 conversion would require additional async handling
                    });
                    return clonedDoc.documentElement.outerHTML;
                });
            }
            else {
                // Get plain HTML content
                htmlContent = await tab.page.content();
            }
            // Process links if needed
            if (params.preserveLinks === false) {
                htmlContent = htmlContent.replace(/href="https?:\/\/[^"]*"/g, 'href="#"');
            }
            // Save HTML content to file
            await fs.writeFile(fileName, htmlContent, 'utf8');
            response.addCode(`
// Save HTML content
const htmlContent = await page.content();
await fs.writeFile(${javascript.formatObject(fileName)}, htmlContent, 'utf8');
      `.trim());
            response.addResult(`Saved HTML content to ${fileName}`);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            response.addResult(`Failed to save HTML: ${errorMessage}`);
            throw error;
        }
    },
});
export default [
    htmlSave,
];
