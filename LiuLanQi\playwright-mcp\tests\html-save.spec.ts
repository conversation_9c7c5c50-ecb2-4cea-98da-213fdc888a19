/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import fs from 'fs';
import path from 'path';

import { test, expect } from './fixtures.js';

test.describe('HTML Save Tool', () => {
  test('should save basic HTML content', async ({ startClient, server }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    // Navigate to test page
    expect(await client.callTool({
      name: 'browser_navigate',
      arguments: { url: server.HELLO_WORLD },
    })).toHaveResponse({
      pageState: expect.stringContaining('Hello, world!'),
    });

    // Test HTML save
    expect(await client.callTool({
      name: 'browser_save_html',
      arguments: {
        filename: 'test-page.html',
        includeResources: false
      },
    })).toHaveResponse({
      result: expect.stringContaining('test-page.html'),
    });

    // Verify file was created
    const files = [...fs.readdirSync(outputDir)];
    const htmlFiles = files.filter(f => f.endsWith('.html'));
    expect(htmlFiles).toHaveLength(1);
    expect(htmlFiles[0]).toBe('test-page.html');

    // Check file content
    const content = fs.readFileSync(path.join(outputDir, 'test-page.html'), 'utf8');
    expect(content).toContain('Hello, world!');
    expect(content).toContain('<html');
    expect(content).toContain('</html>');
  });

  test('should handle custom filename', async ({ startClient, server }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    await client.callTool({
      name: 'browser_navigate',
      arguments: { url: server.HELLO_WORLD },
    });

    expect(await client.callTool({
      name: 'browser_save_html',
      arguments: {
        filename: 'custom-name.html'
      },
    })).toHaveResponse({
      result: expect.stringContaining('custom-name.html'),
    });

    const files = [...fs.readdirSync(outputDir)];
    expect(files).toContain('custom-name.html');
  });

  test('should handle default filename generation', async ({ startClient, server }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    await client.callTool({
      name: 'browser_navigate',
      arguments: { url: server.HELLO_WORLD },
    });

    expect(await client.callTool({
      name: 'browser_save_html',
      arguments: {},
    })).toHaveResponse({
      result: expect.stringMatching(/page-.*\.html/),
    });

    const files = [...fs.readdirSync(outputDir)];
    const htmlFiles = files.filter(f => f.endsWith('.html'));
    expect(htmlFiles).toHaveLength(1);
    expect(htmlFiles[0]).toMatch(/^page-.*\.html$/);
  });

  test('should handle resource inclusion option', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    const testHtml = `
      <html>
        <head>
          <title>Resource Test</title>
          <style>body { color: red; }</style>
        </head>
        <body>
          <h1>Test Page</h1>
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" alt="test">
        </body>
      </html>
    `;

    await client.callTool({
      name: 'browser_navigate',
      arguments: { url: `data:text/html,${encodeURIComponent(testHtml)}` },
    });

    // Test with resources included
    expect(await client.callTool({
      name: 'browser_save_html',
      arguments: {
        filename: 'with-resources.html',
        includeResources: true
      },
    })).toHaveResponse({
      result: expect.stringContaining('with-resources.html'),
    });

    // Test without resources
    expect(await client.callTool({
      name: 'browser_save_html',
      arguments: {
        filename: 'without-resources.html',
        includeResources: false
      },
    })).toHaveResponse({
      result: expect.stringContaining('without-resources.html'),
    });

    // Both files should be created
    const files = [...fs.readdirSync(outputDir)];
    expect(files).toContain('with-resources.html');
    expect(files).toContain('without-resources.html');
  });

  test('should handle errors gracefully', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    // Try to save HTML without navigating to a page first
    const response = await client.callTool({
      name: 'browser_save_html',
      arguments: {
        filename: 'error-test.html'
      },
    });

    // Should handle the error gracefully
    expect(response.isError || response.result.includes('Error')).toBeTruthy();
  });
});
