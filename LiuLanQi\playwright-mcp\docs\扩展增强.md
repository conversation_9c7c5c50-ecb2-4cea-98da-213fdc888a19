您想要扩展和增强 Playwright MCP 的网页下载功能，使其具有更强的通用性和灵活性。 基于当前的代码架构，我来为您提供一个详细的扩展方案。

当前功能分析
目前 Playwright MCP 的下载能力主要包括：

自动下载处理 - 通过 Tab 类的下载事件监听器自动处理文件下载 tab.ts:51-53
PDF 生成 - 使用 browser_pdf_save 工具将页面保存为 PDF pdf.ts:27-54
页面快照 - 通过 PageSnapshot 类获取页面的结构化 YAML 表示 pageSnapshot.ts:42-50
扩展方案
1. 新增 HTML 下载工具
创建一个新的工具来直接获取和保存页面的 HTML 内容：

// src/tools/htmlDownload.ts  
const htmlDownload = defineTool({  
  capability: 'core',  
  schema: {  
    name: 'browser_save_html',  
    title: 'Save HTML',  
    description: 'Save current page as HTML file',  
    inputSchema: z.object({  
      filename: z.string().optional().describe('File name to save the HTML to'),  
      includeResources: z.boolean().optional().describe('Whether to save linked resources (CSS, JS, images)')  
    }),  
    type: 'readOnly',  
  },  
  handle: async (context, params) => {  
    const tab = context.currentTabOrDie();  
    const content = await tab.page.content();  
    const fileName = await outputFile(context.config, params.filename ?? `page-${new Date().toISOString()}.html`);  
      
    // 保存 HTML 内容的逻辑  
  }  
});
2. 新增 Markdown 转换工具
基于现有的页面快照功能，创建 Markdown 转换器：

// src/tools/markdownConverter.ts  
const markdownConverter = defineTool({  
  capability: 'core',  
  schema: {  
    name: 'browser_save_markdown',  
    title: 'Save as Markdown',  
    description: 'Convert current page to Markdown format',  
    inputSchema: z.object({  
      filename: z.string().optional(),  
      includeImages: z.boolean().optional(),  
      preserveLinks: z.boolean().optional()  
    }),  
    type: 'readOnly',  
  },  
  handle: async (context, params) => {  
    // 利用现有的 PageSnapshot 功能  
    // 转换为 Markdown 格式  
  }  
});
3. 扩展下载管理系统
增强 Context 类中的下载管理功能 context.ts:266-275 ：

// 在 Context 类中添加新方法  
async savePageContent(format: 'html' | 'markdown' | 'pdf', options: SaveOptions) {  
  const tab = this.currentTabOrDie();  
    
  switch (format) {  
    case 'html':  
      return await this._saveAsHTML(tab, options);  
    case 'markdown':  
      return await this._saveAsMarkdown(tab, options);  
    case 'pdf':  
      return await this._saveAsPDF(tab, options);  
  }  
}
4. 新增批量下载工具
创建批量处理多个页面的工具：

const batchDownload = defineTool({  
  capability: 'core',  
  schema: {  
    name: 'browser_batch_download',  
    title: 'Batch Download',  
    description: 'Download multiple pages in specified formats',  
    inputSchema: z.object({  
      urls: z.array(z.string()),  
      formats: z.array(z.enum(['html', 'markdown', 'pdf'])),  
      outputDir: z.string().optional()  
    }),  
    type: 'readOnly',  
  }  
});
5. 增强配置系统
扩展配置选项以支持新的下载功能 config.d.ts:94-96 ：

export type Config = {  
  // 现有配置...  
    
  download?: {  
    // 默认下载格式  
    defaultFormat?: 'html' | 'markdown' | 'pdf';  
    // 是否包含资源文件  
    includeResources?: boolean;  
    // Markdown 转换选项  
    markdownOptions?: {  
      preserveLinks?: boolean;  
      includeImages?: boolean;  
      imageFormat?: 'base64' | 'url' | 'download';  
    };  
    // 批量下载并发数  
    concurrency?: number;  
  };  
}
6. 实现资源管理
扩展 outputFile 函数以支持更复杂的文件管理：

// src/config.ts 中扩展  
export async function savePageResources(  
  config: FullConfig,   
  baseFileName: string,   
  resources: Array<{url: string, content: Buffer, type: string}>  
) {  
  // 创建资源目录  
  // 保存相关资源文件  
  // 返回资源映射  
}
7. 工具注册和能力管理
在 src/tools.js 中注册新工具，并在 src/connection.ts 中的工具过滤逻辑中包含这些新功能 connection.ts:29-31 。

实现优先级
第一阶段：实现基础的 HTML 下载功能
第二阶段：添加 Markdown 转换能力
第三阶段：实现批量下载和资源管理
第四阶段：优化性能和用户体验
技术考虑
性能优化：大文件下载时的内存管理
错误处理：网络超时、文件权限等异常情况
格式兼容性：确保生成的文件格式标准化
资源依赖：处理 CSS、JS、图片等外部资源
这个方案将显著增强 Playwright MCP 的网页内容保存能力，提供更灵活和通用的下载选项。

Notes
该扩展方案基于现有的工具系统架构，充分利用了 Tab 类的页面管理能力和 Context 类的执行框架。新功能将与现有的 PDF 生成和文件上传功能形成完整的文件操作生态系统。


+++++++++++++++


增强Playwright MCP下载网页能力的详细方案
基于Playwright MCP代码库分析，有多种方法可以增强其下载网页的能力，以下是详细方案：

方案一：使用Page.captureSnapshot保存完整网页(MHTML)
MHTML (MIME HTML) 是最完整的网页保存格式，包含所有资源(图片、CSS等)，适合离线浏览：

async function saveMHTML(url, filename) {
  // 创建新的浏览器上下文
  const browser = await playwright.chromium.launch();
  const page = await browser.newPage();
  
  // 导航到网页
  await page.goto(url);
  
  // 使用CDP会话捕获快照
  const client = await page.context.newCDPSession(page);
  const { data } = await client.send('Page.captureSnapshot');
  
  // 将MHTML数据保存到文件
  await require('fs').promises.writeFile(filename, data);
  await browser.close();
  
  console.log(`成功将网页保存为: ${filename}`);
}
方案二：使用PDF生成器
Playwright MCP已内置PDF生成功能，在src/tools/pdf.ts中可见实现：

// 使用内置PDF工具
const browserMcpInstance = /* 初始化Playwright MCP实例 */;
await browserMcpInstance.page.pdf({ 
  path: 'page.pdf',
  format: 'A4',
  printBackground: true 
});
方案三：使用JavaScript评估功能保存HTML和资源
使用evaluate工具执行JavaScript代码，可以获取完整HTML并下载相关资源：

async function saveWebpageContent(url, htmlFilename) {
  const browser = await playwright.chromium.launch();
  const page = await browser.newPage();
  
  await page.goto(url);
  
  // 获取完整HTML
  const htmlContent = await page.evaluate(() => {
    return document.documentElement.outerHTML;
  });
  
  // 保存HTML
  await require('fs').promises.writeFile(htmlFilename, htmlContent);
  
  // 可选：获取并保存所有图像等资源
  const resourceUrls = await page.evaluate(() => {
    const imgSrcs = Array.from(document.querySelectorAll('img')).map(img => img.src);
    const cssSrcs = Array.from(document.querySelectorAll('link[rel="stylesheet"]')).map(link => link.href);
    const jsSrcs = Array.from(document.querySelectorAll('script[src]')).map(script => script.src);
    return [...imgSrcs, ...cssSrcs, ...jsSrcs];
  });
  
  // 下载资源文件...
  
  await browser.close();
}
方案四：扩展功能实现
为Playwright MCP开发新工具，添加到src/tools目录：

创建src/tools/webpageSave.ts文件
实现类似以下功能：
import { z } from 'zod';
import { defineTabTool } from './tool.js';
import * as fs from 'fs/promises';
import * as path from 'path';
import { outputFile } from '../config.js';
 
const saveWebpage = defineTabTool({
  capability: 'core',
  
  schema: {
    name: 'browser_save_webpage',
    title: '保存网页',
    description: '保存完整网页内容，包括HTML、CSS、图片等资源',
    inputSchema: z.object({
      filename: z.string().optional().describe('保存的文件名，默认为"page-{timestamp}.mhtml"'),
      format: z.enum(['mhtml', 'html', 'pdf']).optional().describe('保存格式，支持mhtml、html和pdf')
    }),
    type: 'readOnly',
  },
  
  handle: async (tab, params, response) => {
    const format = params.format || 'mhtml';
    const timestamp = new Date().toISOString();
    const filename = await outputFile(
      tab.context.config, 
      params.filename || `page-${timestamp}.${format}`
    );
    
    if (format === 'mhtml') {
      const client = await tab.page.context().newCDPSession(tab.page);
      const { data } = await client.send('Page.captureSnapshot');
      await fs.writeFile(filename, data);
    } else if (format === 'html') {
      const htmlContent = await tab.page.content();
      await fs.writeFile(filename, htmlContent);
    } else if (format === 'pdf') {
      await tab.page.pdf({ path: filename });
    }
    
    response.addResult(`网页已成功保存为 ${filename}`);
  }
});
 
export default [saveWebpage];
最佳实践建议
使用MHTML格式保存最完整的体验，适合离线阅读
使用PDF格式适合存档和打印
使用evaluate功能可以自定义下载内容和行为
实现工具扩展使功能集成到Playwright MCP框架中
通过以上方案，可以显著增强Playwright MCP的网页下载能力，满足各种应用场景的需求。