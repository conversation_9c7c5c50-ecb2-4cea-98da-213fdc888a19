/*
  Copyright (c) Microsoft Corporation.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/

body {
  margin: 0;
  padding: 0;
}

/* Base styles */
.app-container {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif;
  background-color: #ffffff;
  color: #1f2328;
  margin: 0;
  padding: 24px;
  min-height: 100vh;
  font-size: 14px;
  line-height: 1.5;
}

.content-wrapper {
  max-width: 600px;
  margin: 0 auto;
}

.main-title {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f2328;
}

/* Status Banner */
.status-banner {
  padding: 16px;
  margin-bottom: 24px;
  border-radius: 6px;
  border: 1px solid;
  font-size: 14px;
  font-weight: 500;
}

.status-banner.connected {
  background-color: #dafbe1;
  border-color: #1a7f37;
  color: #0d5a23;
}

.status-banner.error {
  background-color: #ffebe9;
  border-color: #da3633;
  color: #a40e26;
}

.status-banner.connecting {
  background-color: #fff8c5;
  border-color: #d1b500;
  color: #7a5c00;
}

/* Buttons */
.button-container {
  margin-bottom: 24px;
}

.button {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  margin-right: 8px;
}

.button.primary {
  background-color: #2da44e;
  border-color: #2da44e;
  color: #ffffff;
}

.button.primary:hover {
  background-color: #2c974b;
}

.button.default {
  background-color: #f6f8fa;
  border-color: #d1d9e0;
  color: #24292f;
}

.button.default:hover {
  background-color: #f3f4f6;
  border-color: #c7d2da;
}

/* Tab selection */
.tab-section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1f2328;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #d1d9e0;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #ffffff;
  cursor: pointer;
}

.tab-item.selected {
  background-color: #f6f8fa;
}

.tab-item.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.tab-radio {
  margin-right: 12px;
  flex-shrink: 0;
}

.tab-favicon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.tab-content {
  flex: 1;
  min-width: 0;
}

.tab-title {
  font-weight: 500;
  color: #1f2328;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-url {
  font-size: 12px;
  color: #656d76;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}