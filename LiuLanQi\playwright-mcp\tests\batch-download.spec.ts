/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import fs from 'fs';
import path from 'path';

import { test, expect } from './fixtures.js';

test.describe('Batch Download Tool', () => {
  test('should handle multiple URLs with HTML format', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    // Create test URLs using data URLs
    const testUrls = [
      'data:text/html,<html><head><title>Page 1</title></head><body><h1>Page 1 Content</h1></body></html>',
      'data:text/html,<html><head><title>Page 2</title></head><body><h1>Page 2 Content</h1></body></html>'
    ];

    expect(await client.callTool({
      name: 'browser_batch_download',
      arguments: {
        urls: testUrls,
        formats: ['html'],
        concurrency: 2,
        filenameTemplate: 'batch-{index}-test'
      },
    })).toHaveResponse({
      result: expect.stringContaining('Batch download completed'),
    });

    // Check if files were created
    const files = [...fs.readdirSync(outputDir)];
    const expectedFiles = [
      'batch-001-test.html',
      'batch-002-test.html'
    ];

    for (const filename of expectedFiles) {
      expect(files).toContain(filename);
      
      // Verify file content
      const content = fs.readFileSync(path.join(outputDir, filename), 'utf8');
      expect(content).toContain('<html');
      expect(content).toContain('</html>');
    }
  });

  test('should handle multiple formats', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    const testUrls = [
      'data:text/html,<html><head><title>Multi Format Test</title></head><body><h1>Test Content</h1></body></html>'
    ];

    expect(await client.callTool({
      name: 'browser_batch_download',
      arguments: {
        urls: testUrls,
        formats: ['html', 'markdown'],
        concurrency: 1,
        filenameTemplate: 'multi-format-{index}'
      },
    })).toHaveResponse({
      result: expect.stringContaining('Batch download completed'),
    });

    // Check if files were created in both formats
    const files = [...fs.readdirSync(outputDir)];
    expect(files).toContain('multi-format-001.html');
    expect(files).toContain('multi-format-001.md');

    // Verify HTML content
    const htmlContent = fs.readFileSync(path.join(outputDir, 'multi-format-001.html'), 'utf8');
    expect(htmlContent).toContain('Test Content');

    // Verify Markdown content
    const mdContent = fs.readFileSync(path.join(outputDir, 'multi-format-001.md'), 'utf8');
    expect(mdContent).toContain('# Test Content');
  });

  test('should handle custom output directory', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const customDir = testInfo.outputPath('custom-batch-output');
    
    const { client } = await startClient({
      config: { outputDir },
    });

    const testUrls = [
      'data:text/html,<html><head><title>Custom Dir Test</title></head><body><h1>Custom Directory</h1></body></html>'
    ];

    expect(await client.callTool({
      name: 'browser_batch_download',
      arguments: {
        urls: testUrls,
        formats: ['html'],
        outputDir: customDir,
        filenameTemplate: 'custom-{index}'
      },
    })).toHaveResponse({
      result: expect.stringContaining('Batch download completed'),
    });

    // Check if custom directory was created and files are there
    expect(fs.existsSync(customDir)).toBeTruthy();
    const files = [...fs.readdirSync(customDir)];
    expect(files).toContain('custom-001.html');
  });

  test('should handle concurrency control', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    // Create multiple test URLs
    const testUrls = Array.from({ length: 5 }, (_, i) => 
      `data:text/html,<html><head><title>Page ${i + 1}</title></head><body><h1>Content ${i + 1}</h1></body></html>`
    );

    expect(await client.callTool({
      name: 'browser_batch_download',
      arguments: {
        urls: testUrls,
        formats: ['html'],
        concurrency: 3,
        filenameTemplate: 'concurrent-{index}'
      },
    })).toHaveResponse({
      result: expect.stringContaining('Batch download completed'),
    });

    // Check if all files were created
    const files = [...fs.readdirSync(outputDir)];
    for (let i = 1; i <= 5; i++) {
      const filename = `concurrent-${i.toString().padStart(3, '0')}.html`;
      expect(files).toContain(filename);
    }
  });

  test('should handle empty URL list', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    expect(await client.callTool({
      name: 'browser_batch_download',
      arguments: {
        urls: [],
        formats: ['html']
      },
    })).toHaveResponse({
      result: expect.stringContaining('No URLs provided'),
    });
  });

  test('should handle empty formats list', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    expect(await client.callTool({
      name: 'browser_batch_download',
      arguments: {
        urls: ['data:text/html,<html><body>Test</body></html>'],
        formats: []
      },
    })).toHaveResponse({
      result: expect.stringContaining('No formats specified'),
    });
  });

  test('should handle invalid URLs gracefully', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    const testUrls = [
      'data:text/html,<html><body>Valid Page</body></html>',
      'invalid-url-that-should-fail'
    ];

    const response = await client.callTool({
      name: 'browser_batch_download',
      arguments: {
        urls: testUrls,
        formats: ['html'],
        filenameTemplate: 'error-test-{index}'
      },
    });

    // Should complete but report failures
    expect(response.result).toContain('Batch download completed');
    expect(response.result).toContain('failed');
  });

  test('should respect filename template placeholders', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    const testUrls = [
      'data:text/html,<html><head><title>Template Test</title></head><body>Test</body></html>'
    ];

    expect(await client.callTool({
      name: 'browser_batch_download',
      arguments: {
        urls: testUrls,
        formats: ['html'],
        filenameTemplate: 'page-{index}-{domain}-test'
      },
    })).toHaveResponse({
      result: expect.stringContaining('Batch download completed'),
    });

    const files = [...fs.readdirSync(outputDir)];
    // Should contain index and domain placeholders replaced
    const expectedPattern = /^page-001-.*-test\.html$/;
    const matchingFiles = files.filter(f => expectedPattern.test(f));
    expect(matchingFiles).toHaveLength(1);
  });
});
