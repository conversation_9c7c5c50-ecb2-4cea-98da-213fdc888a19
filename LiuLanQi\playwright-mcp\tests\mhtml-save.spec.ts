/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import fs from 'fs';
import path from 'path';

import { test, expect } from './fixtures.js';

test.describe('MHTML Save Tool', () => {
  test('should save MHTML content in Chromium', async ({ startClient, server, mcpBrowser }, testInfo) => {
    test.skip(!!mcpBrowser && !['chromium', 'chrome', 'msedge'].includes(mcpBrowser), 'MHTML save is only supported in Chromium-based browsers.');
    
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    // Navigate to test page
    expect(await client.callTool({
      name: 'browser_navigate',
      arguments: { url: server.HELLO_WORLD },
    })).toHaveResponse({
      pageState: expect.stringContaining('Hello, world!'),
    });

    // Test MHTML save
    const response = await client.callTool({
      name: 'browser_save_mhtml',
      arguments: {
        filename: 'test-page.mhtml'
      },
    });

    // MHTML might not work in all test environments, so we handle both success and expected failures
    if (!response.isError) {
      expect(response).toHaveResponse({
        result: expect.stringContaining('test-page.mhtml'),
      });

      // Verify file was created
      const files = [...fs.readdirSync(outputDir)];
      const mhtmlFiles = files.filter(f => f.endsWith('.mhtml'));
      expect(mhtmlFiles).toHaveLength(1);
      expect(mhtmlFiles[0]).toBe('test-page.mhtml');

      // Check file content (MHTML should contain MIME headers)
      const content = fs.readFileSync(path.join(outputDir, 'test-page.mhtml'), 'utf8');
      expect(content).toContain('MIME-Version:');
      expect(content).toContain('Hello, world!');
    } else {
      // If MHTML is not supported, the error should be informative
      expect(response.result).toContain('MHTML');
    }
  });

  test('should handle custom filename', async ({ startClient, server, mcpBrowser }, testInfo) => {
    test.skip(!!mcpBrowser && !['chromium', 'chrome', 'msedge'].includes(mcpBrowser), 'MHTML save is only supported in Chromium-based browsers.');
    
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    await client.callTool({
      name: 'browser_navigate',
      arguments: { url: server.HELLO_WORLD },
    });

    const response = await client.callTool({
      name: 'browser_save_mhtml',
      arguments: {
        filename: 'custom-name.mhtml'
      },
    });

    if (!response.isError) {
      expect(response).toHaveResponse({
        result: expect.stringContaining('custom-name.mhtml'),
      });

      const files = [...fs.readdirSync(outputDir)];
      expect(files).toContain('custom-name.mhtml');
    }
  });

  test('should handle default filename generation', async ({ startClient, server, mcpBrowser }, testInfo) => {
    test.skip(!!mcpBrowser && !['chromium', 'chrome', 'msedge'].includes(mcpBrowser), 'MHTML save is only supported in Chromium-based browsers.');
    
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    await client.callTool({
      name: 'browser_navigate',
      arguments: { url: server.HELLO_WORLD },
    });

    const response = await client.callTool({
      name: 'browser_save_mhtml',
      arguments: {},
    });

    if (!response.isError) {
      expect(response).toHaveResponse({
        result: expect.stringMatching(/page-.*\.mhtml/),
      });

      const files = [...fs.readdirSync(outputDir)];
      const mhtmlFiles = files.filter(f => f.endsWith('.mhtml'));
      expect(mhtmlFiles).toHaveLength(1);
      expect(mhtmlFiles[0]).toMatch(/^page-.*\.mhtml$/);
    }
  });

  test('should handle complex pages with resources', async ({ startClient, mcpBrowser }, testInfo) => {
    test.skip(!!mcpBrowser && !['chromium', 'chrome', 'msedge'].includes(mcpBrowser), 'MHTML save is only supported in Chromium-based browsers.');
    
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    const testHtml = `
      <html>
        <head>
          <title>Complex MHTML Test</title>
          <style>
            body { font-family: Arial, sans-serif; background-color: #f0f0f0; }
            .header { color: blue; font-size: 24px; }
          </style>
        </head>
        <body>
          <div class="header">Complex Page</div>
          <p>This page has embedded styles and images.</p>
          <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwN2FjYyIvPgogIDx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVzdDwvdGV4dD4KICA8L3N2Zz4K" alt="Test SVG">
        </body>
      </html>
    `;

    await client.callTool({
      name: 'browser_navigate',
      arguments: { url: `data:text/html,${encodeURIComponent(testHtml)}` },
    });

    const response = await client.callTool({
      name: 'browser_save_mhtml',
      arguments: {
        filename: 'complex-page.mhtml'
      },
    });

    if (!response.isError) {
      expect(response).toHaveResponse({
        result: expect.stringContaining('complex-page.mhtml'),
      });

      // Verify file was created and contains expected content
      const files = [...fs.readdirSync(outputDir)];
      expect(files).toContain('complex-page.mhtml');

      const content = fs.readFileSync(path.join(outputDir, 'complex-page.mhtml'), 'utf8');
      expect(content).toContain('Complex Page');
      expect(content).toContain('MIME-Version:');
    }
  });

  test('should handle errors gracefully', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    // Try to save MHTML without navigating to a page first
    const response = await client.callTool({
      name: 'browser_save_mhtml',
      arguments: {
        filename: 'error-test.mhtml'
      },
    });

    // Should handle the error gracefully
    expect(response.isError || response.result.includes('Error')).toBeTruthy();
  });
});
