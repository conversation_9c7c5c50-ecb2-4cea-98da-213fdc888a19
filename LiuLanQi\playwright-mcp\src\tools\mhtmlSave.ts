/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { z } from 'zod';
import { defineTabTool } from './tool.js';
import * as fs from 'fs/promises';

import * as javascript from '../javascript.js';

const mhtmlSaveSchema = z.object({
  filename: z.string().optional().describe('File name to save the MHTML to. Defaults to `page-{timestamp}.mhtml` if not specified.'),
});

const mhtmlSave = defineTabTool({
  capability: 'core',

  schema: {
    name: 'browser_save_mhtml',
    title: 'Save as MHTML',
    description: 'Save complete webpage as MHTML file including all resources (images, CSS, JS) for offline viewing',
    inputSchema: mhtmlSaveSchema,
    type: 'readOnly',
  },

  handle: async (tab, params, response) => {
    try {
      const fileName = await tab.context.outputFile(
        params.filename ?? `page-${new Date().toISOString().replace(/[:.]/g, '-')}.mhtml`
      );

      // Use Chrome DevTools Protocol to capture complete page snapshot
      const client = await tab.page.context().newCDPSession(tab.page);
      
      try {
        // Capture the page snapshot as MHTML
        const { data } = await client.send('Page.captureSnapshot', { format: 'mhtml' });
        
        // Save MHTML data to file
        await fs.writeFile(fileName, data, 'utf8');

        response.addCode(`
// Capture complete page as MHTML using CDP
const client = await page.context().newCDPSession(page);
const { data } = await client.send('Page.captureSnapshot', { format: 'mhtml' });
await fs.writeFile(${javascript.formatObject(fileName)}, data, 'utf8');
await client.detach();
        `.trim());

        response.addResult(`Saved complete webpage as MHTML to ${fileName}`);

      } finally {
        // Always detach the CDP session
        await client.detach();
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      // Check if it's a CDP-related error
      if (errorMessage.includes('captureSnapshot')) {
        response.addResult(`Failed to save MHTML: Page snapshot capture not supported. This feature requires Chrome/Chromium browser. Error: ${errorMessage}`);
      } else {
        response.addResult(`Failed to save MHTML: ${errorMessage}`);
      }
      
      throw error;
    }
  },
});

export default [
  mhtmlSave,
];
