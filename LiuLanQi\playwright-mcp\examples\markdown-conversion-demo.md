# Markdown转换工具演示

本文档演示了新开发的`browser_save_markdown`工具的功能和使用方法。

## 功能特性

### 1. 基础HTML到Markdown转换
- ✅ 标题转换 (H1-H6)
- ✅ 段落和文本格式化
- ✅ 粗体和斜体文本
- ✅ 内联代码和代码块
- ✅ 链接处理
- ✅ 图片处理
- ✅ 列表（有序和无序）
- ✅ 表格转换
- ✅ 引用块

### 2. 高级功能
- ✅ 嵌套列表支持
- ✅ 复杂表格结构
- ✅ 内联元素组合
- ✅ 代码语言检测
- ✅ 智能内容区域识别

### 3. 配置选项

#### 图片处理选项
- `url`: 保持原始URL（默认）
- `alt-text`: 仅使用alt文本
- `base64`: 嵌入base64编码（计划中）

#### 链接处理
- `preserveLinks: true`: 保留外部链接（默认）
- `preserveLinks: false`: 移除链接，仅保留文本

#### 其他选项
- `includeImages`: 是否包含图片（默认true）
- `filename`: 自定义文件名

## 使用示例

### 基础使用
```javascript
// 保存当前页面为Markdown
await client.callTool({
  name: 'browser_save_markdown',
  arguments: {
    filename: 'page-content.md'
  }
});
```

### 高级配置
```javascript
// 自定义转换选项
await client.callTool({
  name: 'browser_save_markdown',
  arguments: {
    filename: 'custom-page.md',
    includeImages: true,
    preserveLinks: true,
    imageFormat: 'url'
  }
});
```

## 转换质量

### 支持的HTML元素
- **标题**: `<h1>` - `<h6>` → `#` - `######`
- **段落**: `<p>` → 段落文本
- **格式化**: `<strong>`, `<b>` → `**粗体**`
- **强调**: `<em>`, `<i>` → `*斜体*`
- **代码**: `<code>` → `内联代码`
- **代码块**: `<pre><code>` → 代码块
- **链接**: `<a>` → `[文本](URL)`
- **图片**: `<img>` → `![alt](src)`
- **列表**: `<ul>`, `<ol>` → Markdown列表
- **表格**: `<table>` → Markdown表格
- **引用**: `<blockquote>` → `> 引用文本`
- **分隔线**: `<hr>` → `---`

### 智能处理特性
1. **内容区域识别**: 自动识别主要内容区域（main, article, .content等）
2. **嵌套元素处理**: 正确处理嵌套的格式化元素
3. **表格优化**: 自动添加表头分隔线
4. **代码语言检测**: 从CSS类名中提取代码语言
5. **URL规范化**: 处理相对和绝对URL
6. **文本清理**: 移除多余的空行和空白字符

## 技术实现

### 核心转换算法
- 递归DOM遍历
- 元素类型识别和转换
- 内联元素特殊处理
- 嵌套结构保持

### 性能优化
- 单次页面评估执行
- 最小化DOM操作
- 高效的字符串处理

### 错误处理
- 跨域资源访问保护
- 无效元素跳过
- 格式化失败回退

## 测试覆盖

### 单元测试
- ✅ 基础HTML转换
- ✅ 图片格式选项
- ✅ 链接保留选项
- ✅ 复杂嵌套结构
- ✅ 表格转换
- ✅ 错误处理

### 集成测试
- ✅ 与现有工具系统集成
- ✅ 文件输出管理
- ✅ 配置系统支持

## 已知限制

1. **Base64图片转换**: 当前版本暂不支持自动转换图片为base64
2. **复杂CSS样式**: 不保留CSS样式信息
3. **JavaScript生成内容**: 仅转换静态HTML内容
4. **特殊字符转义**: 部分特殊字符可能需要手动处理

## 未来改进计划

1. **增强图片处理**: 支持图片下载和base64转换
2. **样式保留**: 保留重要的样式信息
3. **自定义转换规则**: 允许用户定义转换规则
4. **批量转换优化**: 提升批量转换性能
5. **更多输出格式**: 支持其他文档格式

## 结论

新开发的Markdown转换工具提供了强大而灵活的网页内容转换能力，支持多种配置选项和高质量的转换结果。工具已经通过了基础测试，可以满足大多数网页内容转换需求。

通过模块化设计和与现有系统的无缝集成，该工具为Playwright MCP项目增加了重要的内容处理能力，为用户提供了更多样化的网页保存选项。
