/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import assert from 'assert';
import http from 'http';
export async function startHttpServer(config) {
    const { host, port } = config;
    const httpServer = http.createServer();
    await new Promise((resolve, reject) => {
        httpServer.on('error', reject);
        httpServer.listen(port, host, () => {
            resolve();
            httpServer.removeListener('error', reject);
        });
    });
    return httpServer;
}
export function httpAddressToString(address) {
    assert(address, 'Could not bind server socket');
    if (typeof address === 'string')
        return address;
    const resolvedPort = address.port;
    let resolvedHost = address.family === 'IPv4' ? address.address : `[${address.address}]`;
    if (resolvedHost === '0.0.0.0' || resolvedHost === '[::]')
        resolvedHost = 'localhost';
    return `http://${resolvedHost}:${resolvedPort}`;
}
