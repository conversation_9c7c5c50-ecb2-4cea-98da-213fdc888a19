/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { z } from 'zod';
import { defineTabTool } from './tool.js';
import * as fs from 'fs/promises';

import * as javascript from '../javascript.js';

const markdownSaveSchema = z.object({
  filename: z.string().optional().describe('File name to save the Markdown to. Defaults to `page-{timestamp}.md` if not specified.'),
  includeImages: z.boolean().optional().describe('Whether to include images in the markdown. Default is true.'),
  preserveLinks: z.boolean().optional().describe('Whether to preserve external links. Default is true.'),
  imageFormat: z.enum(['url', 'alt-text', 'base64']).optional().describe('How to handle images: url (keep original), alt-text (use alt text), base64 (embed as base64). Default is url.'),
});

const markdownSave = defineTabTool({
  capability: 'core',

  schema: {
    name: 'browser_save_markdown',
    title: 'Save as Markdown',
    description: 'Convert current page to Markdown format with customizable options for links and images',
    inputSchema: markdownSaveSchema,
    type: 'readOnly',
  },

  handle: async (tab, params, response) => {
    try {
      const fileName = await tab.context.outputFile(
        params.filename ?? `page-${new Date().toISOString().replace(/[:.]/g, '-')}.md`
      );

      const includeImages = params.includeImages ?? true;
      const preserveLinks = params.preserveLinks ?? true;
      const imageFormat = params.imageFormat ?? 'url';

      // Convert HTML to Markdown using JavaScript evaluation
      const markdownContent = await tab.page.evaluate((options) => {
        const { includeImages, preserveLinks, imageFormat } = options;

        // Enhanced HTML to Markdown converter
        function convertToMarkdown(element: Element, depth: number = 0): string {
          if (!element) return '';

          const tagName = element.tagName.toLowerCase();

          // Handle text nodes and inline elements
          if (element.nodeType === Node.TEXT_NODE) {
            return element.textContent || '';
          }

          // Skip script, style, and other non-content elements
          if (['script', 'style', 'noscript', 'meta', 'link', 'head'].includes(tagName)) {
            return '';
          }

          let result = '';

          switch (tagName) {
            case 'h1':
            case 'h2':
            case 'h3':
            case 'h4':
            case 'h5':
            case 'h6':
              const level = parseInt(tagName.charAt(1));
              const headerText = getTextContent(element).trim();
              if (headerText) {
                result = `${'#'.repeat(level)} ${headerText}\n\n`;
              }
              break;

            case 'p':
              const pText = processInlineElements(element, { preserveLinks, includeImages, imageFormat });
              if (pText.trim()) {
                result = `${pText}\n\n`;
              }
              break;

            case 'div':
            case 'section':
            case 'article':
            case 'main':
              // Process children for container elements
              result = processChildren(element, depth);
              break;

            case 'br':
              result = '\n';
              break;

            case 'hr':
              result = '\n---\n\n';
              break;

            case 'blockquote':
              const quoteText = processInlineElements(element, { preserveLinks, includeImages, imageFormat });
              if (quoteText.trim()) {
                result = `> ${quoteText.replace(/\n/g, '\n> ')}\n\n`;
              }
              break;

            case 'pre':
              const codeElement = element.querySelector('code');
              const codeText = codeElement ? codeElement.textContent : element.textContent;
              const language = codeElement ? getCodeLanguage(codeElement) : '';
              result = `\`\`\`${language}\n${codeText || ''}\n\`\`\`\n\n`;
              break;

            case 'ul':
            case 'ol':
              result = processList(element, tagName === 'ol', depth);
              break;

            case 'table':
              result = processTable(element);
              break;

            case 'img':
              if (includeImages) {
                result = processImage(element as HTMLImageElement, imageFormat);
              }
              break;

            case 'a':
              if (preserveLinks) {
                const href = (element as HTMLAnchorElement).href;
                const linkText = getTextContent(element).trim();
                if (href && linkText) {
                  result = `[${linkText}](${href})`;
                } else {
                  result = linkText;
                }
              } else {
                result = getTextContent(element);
              }
              break;

            case 'strong':
            case 'b':
              const strongText = processInlineElements(element, { preserveLinks, includeImages, imageFormat });
              if (strongText.trim()) {
                result = `**${strongText}**`;
              }
              break;

            case 'em':
            case 'i':
              const emText = processInlineElements(element, { preserveLinks, includeImages, imageFormat });
              if (emText.trim()) {
                result = `*${emText}*`;
              }
              break;

            case 'code':
              const codeInlineText = element.textContent || '';
              result = `\`${codeInlineText}\``;
              break;

            default:
              // For other elements, process children
              result = processChildren(element, depth);
              break;
          }

          return result;
        }

        function processChildren(element: Element, depth: number): string {
          let result = '';
          for (const child of element.children) {
            result += convertToMarkdown(child, depth + 1);
          }
          return result;
        }

        function processInlineElements(element: Element, options: any): string {
          let result = '';

          for (const node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE) {
              result += node.textContent || '';
            } else if (node.nodeType === Node.ELEMENT_NODE) {
              const childElement = node as Element;
              const childTag = childElement.tagName.toLowerCase();

              switch (childTag) {
                case 'strong':
                case 'b':
                  result += `**${getTextContent(childElement)}**`;
                  break;
                case 'em':
                case 'i':
                  result += `*${getTextContent(childElement)}*`;
                  break;
                case 'code':
                  result += `\`${childElement.textContent || ''}\``;
                  break;
                case 'a':
                  if (options.preserveLinks) {
                    const href = (childElement as HTMLAnchorElement).href;
                    const linkText = getTextContent(childElement);
                    result += href ? `[${linkText}](${href})` : linkText;
                  } else {
                    result += getTextContent(childElement);
                  }
                  break;
                case 'img':
                  if (options.includeImages) {
                    result += processImage(childElement as HTMLImageElement, options.imageFormat);
                  }
                  break;
                case 'br':
                  result += '\n';
                  break;
                default:
                  result += getTextContent(childElement);
                  break;
              }
            }
          }

          return result;
        }

        function processList(element: Element, isOrdered: boolean, depth: number): string {
          let result = '';
          const items = element.querySelectorAll(':scope > li');

          items.forEach((li, index) => {
            const marker = isOrdered ? `${index + 1}. ` : '- ';
            const indent = '  '.repeat(depth);
            const itemText = processInlineElements(li, { preserveLinks, includeImages, imageFormat });

            // Handle nested lists
            const nestedLists = li.querySelectorAll(':scope > ul, :scope > ol');
            let nestedContent = '';
            nestedLists.forEach(nestedList => {
              nestedContent += processList(nestedList, nestedList.tagName.toLowerCase() === 'ol', depth + 1);
            });

            result += `${indent}${marker}${itemText.trim()}\n${nestedContent}`;
          });

          return result + '\n';
        }

        function processTable(element: Element): string {
          const rows = element.querySelectorAll('tr');
          if (rows.length === 0) return '';

          let result = '';

          rows.forEach((row, rowIndex) => {
            const cells = row.querySelectorAll('td, th');
            const cellTexts = Array.from(cells).map(cell => {
              return getTextContent(cell).replace(/\|/g, '\\|').trim() || ' ';
            });

            result += `| ${cellTexts.join(' | ')} |\n`;

            // Add header separator after first row
            if (rowIndex === 0 && cells.length > 0) {
              result += `| ${Array(cells.length).fill('---').join(' | ')} |\n`;
            }
          });

          return result + '\n';
        }

        function processImage(img: HTMLImageElement, format: string): string {
          const alt = img.alt || 'Image';
          const src = img.src;

          switch (format) {
            case 'alt-text':
              return `[${alt}]`;
            case 'base64':
              // For now, fall back to URL format
              // Base64 conversion would require additional async handling
              return `![${alt}](${src})`;
            case 'url':
            default:
              return `![${alt}](${src})`;
          }
        }

        function getTextContent(element: Element): string {
          return element.textContent || '';
        }

        function getCodeLanguage(codeElement: Element): string {
          const className = codeElement.className;
          const match = className.match(/language-(\w+)/);
          return match ? match[1] : '';
        }

        // Main conversion logic
        const mainContent = document.querySelector('main, article, .content, #content, [role="main"]') || document.body;

        // Add page title if available
        const title = document.title;
        let markdown = '';

        if (title && title !== 'about:blank') {
          markdown += `# ${title}\n\n`;
        }

        // Add page URL as metadata
        if (window.location.href && !window.location.href.startsWith('data:')) {
          markdown += `*Source: ${window.location.href}*\n\n`;
        }

        // Convert main content
        markdown += convertToMarkdown(mainContent);

        return markdown;
      }, { includeImages, preserveLinks, imageFormat });

      // Clean up the markdown content
      const cleanedMarkdown = markdownContent
        .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
        .trim();

      // Save Markdown content to file
      await fs.writeFile(fileName, cleanedMarkdown, 'utf8');

      response.addCode(`
// Convert page to Markdown
const markdownContent = await page.evaluate(() => {
  // HTML to Markdown conversion logic
  // ... (conversion implementation)
  return convertedMarkdown;
});
await fs.writeFile(${javascript.formatObject(fileName)}, markdownContent, 'utf8');
      `.trim());

      response.addResult(`Converted page to Markdown and saved to ${fileName}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      response.addResult(`Failed to save Markdown: ${errorMessage}`);
      throw error;
    }
  },
});

export default [
  markdownSave,
];
