/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { z } from 'zod';
import { defineTabTool } from './tool.js';
import * as fs from 'fs/promises';

import * as javascript from '../javascript.js';

const markdownSaveSchema = z.object({
  filename: z.string().optional().describe('File name to save the Markdown to. Defaults to `page-{timestamp}.md` if not specified.'),
  includeImages: z.boolean().optional().describe('Whether to include images in the markdown. Default is true.'),
  preserveLinks: z.boolean().optional().describe('Whether to preserve external links. Default is true.'),
  imageFormat: z.enum(['url', 'alt-text', 'base64']).optional().describe('How to handle images: url (keep original), alt-text (use alt text), base64 (embed as base64). Default is url.'),
});

const markdownSave = defineTabTool({
  capability: 'core',

  schema: {
    name: 'browser_save_markdown',
    title: 'Save as Markdown',
    description: 'Convert current page to Markdown format with customizable options for links and images',
    inputSchema: markdownSaveSchema,
    type: 'readOnly',
  },

  handle: async (tab, params, response) => {
    try {
      const fileName = await tab.context.outputFile(
        params.filename ?? `page-${new Date().toISOString().replace(/[:.]/g, '-')}.md`
      );

      const includeImages = params.includeImages ?? true;
      const preserveLinks = params.preserveLinks ?? true;
      const imageFormat = params.imageFormat ?? 'url';

      // Convert HTML to Markdown using JavaScript evaluation
      const markdownContent = await tab.page.evaluate((options) => {
        const { includeImages, preserveLinks, imageFormat } = options;

        function htmlToMarkdown(element: Element): string {
          const tagName = element.tagName.toLowerCase();
          const textContent = element.textContent || '';

          switch (tagName) {
            case 'h1':
              return `# ${textContent}\n\n`;
            case 'h2':
              return `## ${textContent}\n\n`;
            case 'h3':
              return `### ${textContent}\n\n`;
            case 'h4':
              return `#### ${textContent}\n\n`;
            case 'h5':
              return `##### ${textContent}\n\n`;
            case 'h6':
              return `###### ${textContent}\n\n`;
            case 'p':
              return `${textContent}\n\n`;
            case 'br':
              return '\n';
            case 'strong':
            case 'b':
              return `**${textContent}**`;
            case 'em':
            case 'i':
              return `*${textContent}*`;
            case 'code':
              return `\`${textContent}\``;
            case 'pre':
              return `\`\`\`\n${textContent}\n\`\`\`\n\n`;
            case 'blockquote':
              return `> ${textContent}\n\n`;
            case 'a':
              const href = (element as HTMLAnchorElement).href;
              if (preserveLinks && href) {
                return `[${textContent}](${href})`;
              }
              return textContent;
            case 'img':
              if (!includeImages) return '';
              const img = element as HTMLImageElement;
              const alt = img.alt || 'Image';
              const src = img.src;
              
              switch (imageFormat) {
                case 'alt-text':
                  return `[${alt}]`;
                case 'base64':
                  // For base64, we'd need to fetch the image, but that's complex in this context
                  // Fall back to URL format
                  return `![${alt}](${src})`;
                case 'url':
                default:
                  return `![${alt}](${src})`;
              }
            case 'ul':
            case 'ol':
              let listContent = '';
              const listItems = element.querySelectorAll('li');
              listItems.forEach((li, index) => {
                const marker = tagName === 'ul' ? '- ' : `${index + 1}. `;
                listContent += `${marker}${li.textContent}\n`;
              });
              return `${listContent}\n`;
            case 'table':
              let tableContent = '';
              const rows = element.querySelectorAll('tr');
              rows.forEach((row, rowIndex) => {
                const cells = row.querySelectorAll('td, th');
                const cellTexts = Array.from(cells).map(cell => cell.textContent || '');
                tableContent += `| ${cellTexts.join(' | ')} |\n`;
                
                // Add header separator after first row
                if (rowIndex === 0 && cells.length > 0) {
                  tableContent += `| ${Array(cells.length).fill('---').join(' | ')} |\n`;
                }
              });
              return `${tableContent}\n`;
            default:
              // For other elements, process children
              let result = '';
              for (const child of element.children) {
                result += htmlToMarkdown(child);
              }
              return result || textContent;
          }
        }

        // Get the main content area or fall back to body
        const mainContent = document.querySelector('main, article, .content, #content') || document.body;
        
        // Add page title
        const title = document.title;
        let markdown = title ? `# ${title}\n\n` : '';
        
        // Convert content to markdown
        markdown += htmlToMarkdown(mainContent);

        return markdown;
      }, { includeImages, preserveLinks, imageFormat });

      // Clean up the markdown content
      const cleanedMarkdown = markdownContent
        .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
        .trim();

      // Save Markdown content to file
      await fs.writeFile(fileName, cleanedMarkdown, 'utf8');

      response.addCode(`
// Convert page to Markdown
const markdownContent = await page.evaluate(() => {
  // HTML to Markdown conversion logic
  // ... (conversion implementation)
  return convertedMarkdown;
});
await fs.writeFile(${javascript.formatObject(fileName)}, markdownContent, 'utf8');
      `.trim());

      response.addResult(`Converted page to Markdown and saved to ${fileName}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      response.addResult(`Failed to save Markdown: ${errorMessage}`);
      throw error;
    }
  },
});

export default [
  markdownSave,
];
