/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import fs from 'fs';
import path from 'path';

import { test, expect } from './fixtures.js';

test.describe('Download Tools', () => {
  test('browser_save_html should save HTML content', async ({ startClient, server }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    // Navigate to a test page
    expect(await client.callTool({
      name: 'browser_navigate',
      arguments: { url: server.HELLO_WORLD },
    })).toHaveResponse({
      pageState: expect.stringContaining(`- generic [active] [ref=e1]: Hello, world!`),
    });

    // Test HTML save tool
    expect(await client.callTool({
      name: 'browser_save_html',
      arguments: {
        filename: 'test-page.html',
        includeResources: false
      },
    })).toHaveResponse({
      result: expect.stringContaining('test-page.html'),
    });

    // Check if file was created
    const files = [...fs.readdirSync(outputDir)];
    const htmlFiles = files.filter(f => f.endsWith('.html'));
    expect(htmlFiles).toHaveLength(1);
    expect(htmlFiles[0]).toBe('test-page.html');

    // Check file content
    const content = fs.readFileSync(path.join(outputDir, 'test-page.html'), 'utf8');
    expect(content).toContain('Hello, world!');
  });

  test('browser_save_mhtml should save complete webpage', async () => {
    const { context, page } = await createTestContext();
    
    // Navigate to a test page
    await page.setContent(`
      <html>
        <head><title>MHTML Test Page</title></head>
        <body>
          <h1>MHTML Test</h1>
          <p>This is a test for MHTML saving</p>
        </body>
      </html>
    `);

    // Test MHTML save tool
    const response = await context.runTool('browser_save_mhtml', {
      filename: 'test-page.mhtml'
    });

    // Note: MHTML might not work in all test environments
    // The test should handle both success and expected failures gracefully
    if (!response.isError) {
      const outputDir = path.dirname(await context.outputFile('temp.txt'));
      const mhtmlFile = path.join(outputDir, 'test-page.mhtml');
      
      const fileExists = await fs.access(mhtmlFile).then(() => true).catch(() => false);
      expect(fileExists).toBeTruthy();
    }
  });

  test('browser_save_markdown should convert HTML to markdown', async () => {
    const { context, page } = await createTestContext();
    
    // Navigate to a test page with various HTML elements
    await page.setContent(`
      <html>
        <head><title>Markdown Test Page</title></head>
        <body>
          <h1>Main Heading</h1>
          <h2>Sub Heading</h2>
          <p>This is a paragraph with <strong>bold text</strong> and <em>italic text</em>.</p>
          <ul>
            <li>List item 1</li>
            <li>List item 2</li>
          </ul>
          <a href="https://example.com">External Link</a>
          <img src="test.jpg" alt="Test Image">
        </body>
      </html>
    `);

    // Test Markdown save tool
    const response = await context.runTool('browser_save_markdown', {
      filename: 'test-page.md',
      includeImages: true,
      preserveLinks: true
    });

    expect(response.isError).toBeFalsy();
    
    // Check if file was created
    const outputDir = path.dirname(await context.outputFile('temp.txt'));
    const markdownFile = path.join(outputDir, 'test-page.md');
    
    const fileExists = await fs.access(markdownFile).then(() => true).catch(() => false);
    expect(fileExists).toBeTruthy();

    if (fileExists) {
      const content = await fs.readFile(markdownFile, 'utf8');
      expect(content).toContain('# Main Heading');
      expect(content).toContain('## Sub Heading');
      expect(content).toContain('**bold text**');
      expect(content).toContain('*italic text*');
    }
  });

  test('browser_batch_download should handle multiple URLs', async () => {
    const { context } = await createTestContext();
    
    // Create a simple test server or use data URLs
    const testUrls = [
      'data:text/html,<html><head><title>Page 1</title></head><body><h1>Page 1</h1></body></html>',
      'data:text/html,<html><head><title>Page 2</title></head><body><h1>Page 2</h1></body></html>'
    ];

    // Test batch download tool
    const response = await context.runTool('browser_batch_download', {
      urls: testUrls,
      formats: ['html', 'markdown'],
      concurrency: 2,
      filenameTemplate: 'batch-{index}-test'
    });

    expect(response.isError).toBeFalsy();
    
    // Check if files were created
    const outputDir = path.dirname(await context.outputFile('temp.txt'));
    
    // Check for expected files
    const expectedFiles = [
      'batch-001-test.html',
      'batch-001-test.md',
      'batch-002-test.html',
      'batch-002-test.md'
    ];

    for (const filename of expectedFiles) {
      const filepath = path.join(outputDir, filename);
      const fileExists = await fs.access(filepath).then(() => true).catch(() => false);
      expect(fileExists).toBeTruthy();
    }
  });

  test('download tools should handle errors gracefully', async () => {
    const { context } = await createTestContext();
    
    // Test with invalid parameters
    const response = await context.runTool('browser_batch_download', {
      urls: [],
      formats: ['html']
    });

    // Should not throw an error, but should report the issue
    expect(response.isError).toBeFalsy();
    expect(response.content.some(c => 
      c.type === 'text' && c.text.includes('No URLs provided')
    )).toBeTruthy();
  });
});
