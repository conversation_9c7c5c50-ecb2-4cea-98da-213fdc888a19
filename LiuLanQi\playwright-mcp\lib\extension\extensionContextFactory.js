/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import debug from 'debug';
import * as playwright from 'playwright';
import { startHttpServer } from '../httpServer.js';
import { CDPRelayServer } from './cdpRelay.js';
const debugLogger = debug('pw:mcp:relay');
export class ExtensionContextFactory {
    name = 'extension';
    description = 'Connect to a browser using the Playwright MCP extension';
    _browserChannel;
    _relayPromise;
    _browserPromise;
    constructor(browserChannel) {
        this._browserChannel = browserChannel;
    }
    async createContext(clientInfo, abortSignal) {
        // First call will establish the connection to the extension.
        if (!this._browserPromise)
            this._browserPromise = this._obtainBrowser(clientInfo, abortSignal);
        const browser = await this._browserPromise;
        return {
            browserContext: browser.contexts()[0],
            close: async () => {
                debugLogger('close() called for browser context');
                await browser.close();
                this._browserPromise = undefined;
            }
        };
    }
    async _obtainBrowser(clientInfo, abortSignal) {
        if (!this._relayPromise)
            this._relayPromise = this._startRelay(abortSignal);
        const relay = await this._relayPromise;
        abortSignal.throwIfAborted();
        await relay.ensureExtensionConnectionForMCPContext(clientInfo, abortSignal);
        const browser = await playwright.chromium.connectOverCDP(relay.cdpEndpoint());
        browser.on('disconnected', () => {
            this._browserPromise = undefined;
            debugLogger('Browser disconnected');
        });
        return browser;
    }
    async _startRelay(abortSignal) {
        const httpServer = await startHttpServer({});
        const cdpRelayServer = new CDPRelayServer(httpServer, this._browserChannel);
        debugLogger(`CDP relay server started, extension endpoint: ${cdpRelayServer.extensionEndpoint()}.`);
        if (abortSignal.aborted)
            cdpRelayServer.stop();
        else
            abortSignal.addEventListener('abort', () => cdpRelayServer.stop());
        return cdpRelayServer;
    }
}
