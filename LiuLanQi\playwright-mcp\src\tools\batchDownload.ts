/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { z } from 'zod';
import { defineTool } from './tool.js';
import * as fs from 'fs/promises';
import * as path from 'path';

import * as javascript from '../javascript.js';

const batchDownloadSchema = z.object({
  urls: z.array(z.string().url()).describe('Array of URLs to download'),
  formats: z.array(z.enum(['html', 'mhtml', 'markdown', 'pdf'])).describe('Formats to save each page in'),
  outputDir: z.string().optional().describe('Output directory for downloaded files. Defaults to current output directory.'),
  concurrency: z.number().min(1).max(10).optional().describe('Number of concurrent downloads (1-10). Default is 3.'),
  filenameTemplate: z.string().optional().describe('Template for filenames. Use {index}, {url}, {domain} placeholders. Default is "page-{index}-{domain}".'),
});

const batchDownload = defineTool({
  capability: 'core',

  schema: {
    name: 'browser_batch_download',
    title: 'Batch Download Pages',
    description: 'Download multiple pages in specified formats with concurrent processing',
    inputSchema: batchDownloadSchema,
    type: 'readOnly',
  },

  handle: async (context, params, response) => {
    try {
      const { urls, formats, outputDir, concurrency = 3, filenameTemplate = 'page-{index}-{domain}' } = params;
      
      if (urls.length === 0) {
        response.addResult('No URLs provided for batch download');
        return;
      }

      if (formats.length === 0) {
        response.addResult('No formats specified for batch download');
        return;
      }

      // Create output directory if specified
      let baseOutputDir: string;
      if (outputDir) {
        baseOutputDir = path.resolve(outputDir);
        await fs.mkdir(baseOutputDir, { recursive: true });
      } else {
        // Use the context's output directory
        baseOutputDir = path.dirname(await context.outputFile('temp.txt'));
      }

      response.addResult(`Starting batch download of ${urls.length} URLs in ${formats.length} format(s)`);
      response.addResult(`Output directory: ${baseOutputDir}`);
      response.addResult(`Concurrency: ${concurrency}`);

      const results: Array<{ url: string; success: boolean; files: string[]; error?: string }> = [];
      
      // Process URLs in batches with concurrency control
      for (let i = 0; i < urls.length; i += concurrency) {
        const batch = urls.slice(i, i + concurrency);
        const batchPromises = batch.map(async (url, batchIndex) => {
          const urlIndex = i + batchIndex;
          const urlResult = { url, success: false, files: [] as string[], error: undefined as string | undefined };
          
          try {
            // Create a new tab for this URL
            const tab = await context.newTab();
            await tab.navigate(url);
            
            // Wait for page to load
            await tab.page.waitForLoadState('networkidle', { timeout: 30000 });
            
            // Generate filename base
            const urlObj = new URL(url);
            const domain = urlObj.hostname.replace(/[^a-zA-Z0-9]/g, '-');
            const filenameBase = filenameTemplate
              .replace('{index}', (urlIndex + 1).toString().padStart(3, '0'))
              .replace('{url}', url.replace(/[^a-zA-Z0-9]/g, '-'))
              .replace('{domain}', domain);

            // Save in each requested format
            for (const format of formats) {
              const filename = `${filenameBase}.${format}`;
              const filepath = path.join(baseOutputDir, filename);
              
              try {
                switch (format) {
                  case 'html':
                    const htmlContent = await tab.page.content();
                    await fs.writeFile(filepath, htmlContent, 'utf8');
                    break;
                    
                  case 'mhtml':
                    const client = await tab.page.context().newCDPSession(tab.page);
                    try {
                      const { data } = await client.send('Page.captureSnapshot', { format: 'mhtml' });
                      await fs.writeFile(filepath, data, 'utf8');
                    } finally {
                      await client.detach();
                    }
                    break;
                    
                  case 'markdown':
                    // Simple markdown conversion (basic implementation)
                    const markdownContent = await tab.page.evaluate(() => {
                      const title = document.title;
                      const bodyText = document.body.innerText;
                      return `# ${title}\n\n${bodyText}`;
                    });
                    await fs.writeFile(filepath, markdownContent, 'utf8');
                    break;
                    
                  case 'pdf':
                    await tab.page.pdf({ path: filepath });
                    break;
                }
                
                urlResult.files.push(filepath);
              } catch (formatError) {
                const errorMsg = formatError instanceof Error ? formatError.message : 'Unknown error';
                response.addResult(`Failed to save ${url} as ${format}: ${errorMsg}`);
              }
            }
            
            // Close the tab
            await tab.page.close();
            urlResult.success = true;
            
          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            urlResult.error = errorMsg;
            response.addResult(`Failed to process ${url}: ${errorMsg}`);
          }
          
          return urlResult;
        });
        
        // Wait for current batch to complete
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // Progress update
        const completed = Math.min(i + concurrency, urls.length);
        response.addResult(`Progress: ${completed}/${urls.length} URLs processed`);
      }

      // Summary
      const successful = results.filter(r => r.success).length;
      const failed = results.length - successful;
      const totalFiles = results.reduce((sum, r) => sum + r.files.length, 0);

      response.addCode(`
// Batch download completed
// URLs processed: ${urls.length}
// Successful: ${successful}
// Failed: ${failed}
// Total files created: ${totalFiles}
      `.trim());

      response.addResult(`Batch download completed: ${successful} successful, ${failed} failed, ${totalFiles} files created`);

      if (failed > 0) {
        const failedUrls = results.filter(r => !r.success).map(r => `${r.url}: ${r.error}`);
        response.addResult(`Failed URLs:\n${failedUrls.join('\n')}`);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      response.addResult(`Batch download failed: ${errorMessage}`);
      throw error;
    }
  },
});

export default [
  batchDownload,
];
