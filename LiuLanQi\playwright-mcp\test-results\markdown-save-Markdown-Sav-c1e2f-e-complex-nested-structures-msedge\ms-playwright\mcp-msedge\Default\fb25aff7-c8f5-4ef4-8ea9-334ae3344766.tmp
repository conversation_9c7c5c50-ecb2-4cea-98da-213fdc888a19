{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_info": [{"access_point": 17, "account_id": "000300000B9B35B3", "accountcapabilities": {"accountcapabilities/g42tslldmfya": -1, "accountcapabilities/g44tilldmfya": -1, "accountcapabilities/ge2dinbnmnqxa": -1, "accountcapabilities/ge2tkmznmnqxa": -1, "accountcapabilities/ge2tknznmnqxa": -1, "accountcapabilities/ge2tkobnmnqxa": -1, "accountcapabilities/ge3dgmjnmnqxa": -1, "accountcapabilities/ge3dgobnmnqxa": -1, "accountcapabilities/geydgnznmnqxa": -1, "accountcapabilities/geytcnbnmnqxa": -1, "accountcapabilities/gezdcnbnmnqxa": -1, "accountcapabilities/gezdsmbnmnqxa": -1, "accountcapabilities/geztenjnmnqxa": -1, "accountcapabilities/gi2tklldmfya": -1, "accountcapabilities/gu2dqlldmfya": -1, "accountcapabilities/gu4dmlldmfya": -1, "accountcapabilities/guydolldmfya": -1, "accountcapabilities/guzdslldmfya": -1, "accountcapabilities/haytqlldmfya": -1, "accountcapabilities/he4tolldmfya": -1}, "edge_account_age_group": 0, "edge_account_cid": "851b52c14b6dbf78", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "S", "edge_account_is_test_on_premises_profile": false, "edge_account_last_name": "W", "edge_account_location": "", "edge_account_oid": "", "edge_account_phone_number": "", "edge_account_puid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_tenant_supports_msa_linking": false, "edge_wam_aad_for_app_account_type": 0, "email": "<EMAIL>", "full_name": "", "gaia": "000300000B9B35B3", "given_name": "", "hd": "", "is_supervised_child": -1, "is_under_advanced_protection": false, "locale": "", "picture_url": ""}], "alternate_error_pages": {"backup": true}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"available_dark_theme_options": "All", "edge_sidebar_visibility": {"add_app_to_bottom": true}, "editor_proofing_languages": {}, "has_seen_welcome_page": false, "recent_theme_color_list": [**********.0, **********.0, **********.0, **********.0, **********.0]}, "browser_content_container_height": 491, "browser_content_container_width": 764, "browser_content_container_x": 0, "browser_content_container_y": 81, "commerce_daily_metrics_last_update_time": "*****************", "continuous_migration": {"equal_opt_out_users_data": {"backfilled": true}}, "countryid_at_install": 17230, "custom_links": {"list": []}, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "edge": {"msa_sso_info": {"allow_for_non_msa_profile": true}, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 1}, "profile_sso_option": 1, "services": {"signin_scoped_device_id": "c1db77cc-6706-414f-a4de-b0260b62e6cf"}}, "edge_rewards": {"cache_data": "CAA=", "refresh_status_muted_until": "13399352005596354"}, "edge_wallet": {"trigger_funnel": {"records": []}}, "enterprise_profile_guid": "8252ee8e-6be3-467e-900b-18ca94f5b7b1", "extension": {"installed_extension_count": 4}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.3351.65", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": []}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "intl": {"selected_languages": "zh-CN,en,en-GB,en-US"}, "media": {"engagement": {"schema_version": 5}}, "muid": {"last_sync": "13398747206111419"}, "optimization_guide": {"previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.3351.65", "creation_time": "13398747205393916", "edge_profile_id": "a02a81e2-7198-4b33-9fe1-fae8085a0e65", "has_seen_signin_fre": false, "managed_user_id": "", "name": "用户配置 1", "network_pbs": {}, "signin_fre_seen_time": "13398747205584357"}, "reset_prepopulated_engines": false, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false}, "sessions": {"event_log": [{"crashed": false, "time": "*****************", "type": 0}], "session_data_status": 1}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "*****************"}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["zh-CN", "en-US"], "dictionary": ""}, "sync": {"apps": true, "autofill": true, "bookmarks": true, "collections": true, "collections_edge_re_evaluated": true, "collections_edge_supported": true, "edge_account_type": 0, "edge_wallet": true, "edge_wallet_edge_supported": true, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "extensions_edge_supported": true, "history_edge_supported": true, "keep_everything_synced": false, "passwords": true, "passwords_per_account_pref_migration_done": true, "preferences": true, "tabs": true, "tabs_edge_supported": true, "typed_urls": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled_last_known_value": false}}