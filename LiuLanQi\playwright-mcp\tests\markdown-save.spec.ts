/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import fs from 'fs';
import path from 'path';

import { test, expect } from './fixtures.js';

test.describe('Markdown Save Tool', () => {
  test('should convert basic HTML to Markdown', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    // Create a test page with various HTML elements
    const testHtml = `
      <html>
        <head><title>Test Markdown Conversion</title></head>
        <body>
          <h1>Main Heading</h1>
          <h2>Sub Heading</h2>
          <p>This is a paragraph with <strong>bold text</strong> and <em>italic text</em>.</p>
          <p>Another paragraph with <code>inline code</code>.</p>
          <ul>
            <li>First list item</li>
            <li>Second list item</li>
            <li>Third list item</li>
          </ul>
          <ol>
            <li>Numbered item 1</li>
            <li>Numbered item 2</li>
          </ol>
          <blockquote>This is a blockquote with some important information.</blockquote>
          <a href="https://example.com">External Link</a>
          <img src="test.jpg" alt="Test Image">
        </body>
      </html>
    `;

    // Navigate to the test page
    expect(await client.callTool({
      name: 'browser_navigate',
      arguments: { url: `data:text/html,${encodeURIComponent(testHtml)}` },
    })).toHaveResponse({
      pageState: expect.stringContaining('Main Heading'),
    });

    // Test Markdown conversion
    expect(await client.callTool({
      name: 'browser_save_markdown',
      arguments: {
        filename: 'test-conversion.md',
        includeImages: true,
        preserveLinks: true,
        imageFormat: 'url'
      },
    })).toHaveResponse({
      result: expect.stringContaining('test-conversion.md'),
    });

    // Verify file was created
    const files = [...fs.readdirSync(outputDir)];
    const markdownFiles = files.filter(f => f.endsWith('.md'));
    expect(markdownFiles).toHaveLength(1);
    expect(markdownFiles[0]).toBe('test-conversion.md');

    // Check file content
    const content = fs.readFileSync(path.join(outputDir, 'test-conversion.md'), 'utf8');
    
    // Verify title conversion
    expect(content).toContain('# Test Markdown Conversion');
    
    // Verify headings
    expect(content).toContain('# Main Heading');
    expect(content).toContain('## Sub Heading');
    
    // Verify text formatting
    expect(content).toContain('**bold text**');
    expect(content).toContain('*italic text*');
    expect(content).toContain('`inline code`');
    
    // Verify lists
    expect(content).toContain('- First list item');
    expect(content).toContain('1. Numbered item 1');
    
    // Verify blockquote
    expect(content).toContain('> This is a blockquote');
    
    // Verify links
    expect(content).toContain('[External Link](https://example.com)');
    
    // Verify images
    expect(content).toContain('![Test Image](test.jpg)');
  });

  test('should handle image format options', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    const testHtml = `
      <html>
        <head><title>Image Test</title></head>
        <body>
          <h1>Image Format Test</h1>
          <img src="https://example.com/image.jpg" alt="Example Image">
        </body>
      </html>
    `;

    await client.callTool({
      name: 'browser_navigate',
      arguments: { url: `data:text/html,${encodeURIComponent(testHtml)}` },
    });

    // Test alt-text format
    expect(await client.callTool({
      name: 'browser_save_markdown',
      arguments: {
        filename: 'alt-text-test.md',
        imageFormat: 'alt-text'
      },
    })).toHaveResponse({
      result: expect.stringContaining('alt-text-test.md'),
    });

    const altTextContent = fs.readFileSync(path.join(outputDir, 'alt-text-test.md'), 'utf8');
    expect(altTextContent).toContain('[Example Image]');
    expect(altTextContent).not.toContain('![Example Image]');
  });

  test('should handle links preservation option', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    const testHtml = `
      <html>
        <head><title>Links Test</title></head>
        <body>
          <h1>Links Test</h1>
          <p>Visit <a href="https://example.com">our website</a> for more info.</p>
        </body>
      </html>
    `;

    await client.callTool({
      name: 'browser_navigate',
      arguments: { url: `data:text/html,${encodeURIComponent(testHtml)}` },
    });

    // Test with links preserved
    await client.callTool({
      name: 'browser_save_markdown',
      arguments: {
        filename: 'links-preserved.md',
        preserveLinks: true
      },
    });

    // Test with links removed
    await client.callTool({
      name: 'browser_save_markdown',
      arguments: {
        filename: 'links-removed.md',
        preserveLinks: false
      },
    });

    const preservedContent = fs.readFileSync(path.join(outputDir, 'links-preserved.md'), 'utf8');
    const removedContent = fs.readFileSync(path.join(outputDir, 'links-removed.md'), 'utf8');

    expect(preservedContent).toContain('[our website](https://example.com)');
    expect(removedContent).toContain('our website');
    expect(removedContent).not.toContain('[our website](https://example.com)');
  });

  test('should handle complex nested structures', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    const testHtml = `
      <html>
        <head><title>Complex Structure Test</title></head>
        <body>
          <article>
            <h1>Article Title</h1>
            <section>
              <h2>Section 1</h2>
              <p>Paragraph with <strong>nested <em>formatting</em></strong>.</p>
              <ul>
                <li>Item 1 with <a href="https://example.com">link</a></li>
                <li>Item 2
                  <ul>
                    <li>Nested item 1</li>
                    <li>Nested item 2</li>
                  </ul>
                </li>
              </ul>
            </section>
            <section>
              <h2>Code Example</h2>
              <pre><code class="language-javascript">
function hello() {
  console.log("Hello, world!");
}
              </code></pre>
            </section>
          </article>
        </body>
      </html>
    `;

    await client.callTool({
      name: 'browser_navigate',
      arguments: { url: `data:text/html,${encodeURIComponent(testHtml)}` },
    });

    expect(await client.callTool({
      name: 'browser_save_markdown',
      arguments: {
        filename: 'complex-structure.md',
      },
    })).toHaveResponse({
      result: expect.stringContaining('complex-structure.md'),
    });

    const content = fs.readFileSync(path.join(outputDir, 'complex-structure.md'), 'utf8');
    
    // Verify nested formatting
    expect(content).toContain('**nested *formatting***');
    
    // Verify code blocks
    expect(content).toContain('```javascript');
    expect(content).toContain('function hello()');
    
    // Verify nested lists
    expect(content).toContain('- Item 1 with [link](https://example.com)');
    expect(content).toContain('  - Nested item 1');
  });

  test('should handle tables correctly', async ({ startClient }, testInfo) => {
    const outputDir = testInfo.outputPath('output');
    const { client } = await startClient({
      config: { outputDir },
    });

    const testHtml = `
      <html>
        <head><title>Table Test</title></head>
        <body>
          <h1>Table Example</h1>
          <table>
            <tr>
              <th>Name</th>
              <th>Age</th>
              <th>City</th>
            </tr>
            <tr>
              <td>John</td>
              <td>30</td>
              <td>New York</td>
            </tr>
            <tr>
              <td>Jane</td>
              <td>25</td>
              <td>London</td>
            </tr>
          </table>
        </body>
      </html>
    `;

    await client.callTool({
      name: 'browser_navigate',
      arguments: { url: `data:text/html,${encodeURIComponent(testHtml)}` },
    });

    expect(await client.callTool({
      name: 'browser_save_markdown',
      arguments: {
        filename: 'table-test.md',
      },
    })).toHaveResponse({
      result: expect.stringContaining('table-test.md'),
    });

    const content = fs.readFileSync(path.join(outputDir, 'table-test.md'), 'utf8');
    
    // Verify table structure
    expect(content).toContain('| Name | Age | City |');
    expect(content).toContain('| --- | --- | --- |');
    expect(content).toContain('| John | 30 | New York |');
    expect(content).toContain('| Jane | 25 | London |');
  });
});
